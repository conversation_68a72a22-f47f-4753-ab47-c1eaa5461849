import time
from playwright.sync_api import sync_playwright

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    page = browser.new_page()

    # 导航到你想要的网页
    page.goto("https://www.bilibili.com")

    # 打印页面的标题
    print(page.title())

    # 浏览器将保持打开状态 20 秒
    print("浏览器将保持打开状态 20 秒...")
    time.sleep(20)

    # 关闭浏览器
    browser.close()
    print("浏览器已关闭。")

with sync_playwright() as playwright:
    run(playwright)