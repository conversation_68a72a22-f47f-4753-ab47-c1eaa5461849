import time
import threading
from playwright.sync_api import sync_playwright

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    page = browser.new_page()

    # 导航到你想要的网页
    page.goto("https://www.bilibili.com")

    # 打印页面的标题
    print(page.title())

    # 等待用户按下回车键
    print("浏览器已打开，按下回车键关闭浏览器...")

    try:
        # 简单等待用户按回车
        input("按回车键关闭浏览器...")
    except KeyboardInterrupt:
        print("\n检测到Ctrl+C")
    except Exception as e:
        print(f"\n输入检测出错: {e}")

    # 关闭浏览器
    browser.close()
    print("浏览器已关闭。")

with sync_playwright() as playwright:
    run(playwright)