import time
from playwright.sync_api import sync_playwright

def find_and_click_refresh_button(page):
    """寻找并点击'换一换'按钮"""
    print("正在寻找'换一换'按钮...")
    time.sleep(1)

    # 首选：使用官方推荐的 get_by_* 方法
    try:
        refresh_button = page.get_by_role("button", name="换一换")
        if refresh_button.is_visible():
            refresh_button.click()
            print("成功点击'换一换'按钮 (get_by_role)")
            time.sleep(1)
            return True
    except Exception:
        pass

    try:
        refresh_button = page.get_by_text("换一换")
        if refresh_button.is_visible():
            refresh_button.click()
            print("成功点击'换一换'按钮 (get_by_text)")
            time.sleep(1)
            return True
    except Exception:
        pass

    # 次选：使用简单的CSS选择器
    css_selectors = [
        "button.roll-btn",
        "button:has-text('换一换')",
        ".roll-btn"
    ]

    for selector in css_selectors:
        try:
            element = page.locator(selector).first
            if element.is_visible():
                element.click()
                print(f"成功点击'换一换'按钮 ({selector})")
                time.sleep(1)
                return True
        except Exception:
            continue

    print("未找到'换一换'按钮")
    return False

def wait_for_browser_close(page):
    """等待浏览器关闭"""
    print("浏览器保持打开，关闭浏览器窗口即可终止脚本")

    while True:
        try:
            page.title()
            time.sleep(0.5)
        except Exception:
            print("浏览器已关闭，脚本退出")
            break

def run(playwright):
    """主运行函数"""
    print("开始执行脚本...")

    browser = playwright.chromium.launch(headless=False)
    page = browser.new_page()

    try:
        # 导航到B站
        print("正在打开B站...")
        page.goto("https://www.bilibili.com")
        print(f"页面标题: {page.title()}")
        time.sleep(1)

        # 等待页面加载完成
        page.wait_for_load_state('networkidle')
        time.sleep(1)

        # 寻找并点击按钮
        success = find_and_click_refresh_button(page)

        if success:
            print("任务执行成功")
        else:
            print("任务执行失败")

        # 等待用户关闭浏览器
        wait_for_browser_close(page)

    except KeyboardInterrupt:
        print("检测到Ctrl+C，关闭浏览器...")
        browser.close()
    except Exception as e:
        print(f"发生错误: {e}")
        wait_for_browser_close(page)

    print("脚本执行完毕")

with sync_playwright() as playwright:
    run(playwright)