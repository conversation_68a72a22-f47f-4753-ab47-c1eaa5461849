import time
import keyboard
from playwright.sync_api import sync_playwright

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    page = browser.new_page()

    # 导航到你想要的网页
    page.goto("https://www.bilibili.com")

    # 打印页面的标题
    print(page.title())

    # 等待用户按下ESC键
    print("浏览器已打开，按下ESC键关闭浏览器...")

    # 监听ESC键
    while True:
        if keyboard.is_pressed('esc'):
            break
        time.sleep(0.1)  # 短暂休眠以减少CPU使用

    # 关闭浏览器
    browser.close()
    print("浏览器已关闭。")

with sync_playwright() as playwright:
    run(playwright)