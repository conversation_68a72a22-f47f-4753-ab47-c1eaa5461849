import time
from playwright.sync_api import sync_playwright

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    page = browser.new_page()

    # 导航到你想要的网页
    page.goto("https://www.bilibili.com")

    # 打印页面的标题
    print(page.title())
    print("浏览器已打开，直接关闭浏览器窗口即可终止脚本...")

    try:
        # 持续检查浏览器是否还在运行
        while True:
            try:
                # 尝试获取页面标题，如果浏览器被关闭，这会抛出异常
                page.title()
                time.sleep(0.5)  # 每0.5秒检查一次
            except Exception:
                # 浏览器已被关闭
                print("检测到浏览器已关闭，脚本即将退出...")
                break

    except KeyboardInterrupt:
        print("\n检测到Ctrl+C，正在关闭浏览器...")
        browser.close()
    except Exception as e:
        print(f"\n发生错误: {e}")
        browser.close()

    print("脚本已终止。")

with sync_playwright() as playwright:
    run(playwright)