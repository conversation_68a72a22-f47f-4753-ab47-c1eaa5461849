import time
from playwright.sync_api import sync_playwright

def find_and_click_refresh_button(page):
    """寻找并点击'换一换'按钮"""
    print("🔍 正在寻找'换一换'按钮...")
    time.sleep(2)  # 给用户时间看到这个步骤

    # 首选：使用官方推荐的 get_by_* 方法
    print("📋 尝试方法1: 通过角色和文本查找按钮...")
    time.sleep(1)
    try:
        refresh_button = page.get_by_role("button", name="换一换")
        if refresh_button.is_visible():
            print("✅ 找到按钮！准备点击...")
            time.sleep(1)
            refresh_button.click()
            print("✅ 成功点击'换一换'按钮！(使用 get_by_role)")
            time.sleep(2)  # 点击后等待
            return True
        else:
            print("⚠️  按钮不可见，尝试下一种方法...")
    except Exception as e:
        print(f"❌ 方法1失败: {e}")

    time.sleep(1)

    # 添加 get_by_text 方法
    print("📋 尝试方法2: 通过文本内容查找...")
    time.sleep(1)
    try:
        refresh_button = page.get_by_text("换一换")
        if refresh_button.is_visible():
            print("✅ 找到按钮！准备点击...")
            time.sleep(1)
            refresh_button.click()
            print("✅ 成功点击'换一换'按钮！(使用 get_by_text)")
            time.sleep(2)  # 点击后等待
            return True
        else:
            print("⚠️  按钮不可见，尝试CSS选择器...")
    except Exception as e:
        print(f"❌ 方法2失败: {e}")

    time.sleep(1)

    # 次选：使用简单的CSS选择器
    css_selectors = [
        "button.roll-btn",           # 基于类名
        "button:has-text('换一换')",   # 基于文本内容
        ".roll-btn"                  # 简化的类选择器
    ]

    print("📋 尝试CSS选择器方法...")
    for i, selector in enumerate(css_selectors, 1):
        print(f"🔍 尝试CSS选择器 {i}: {selector}")
        time.sleep(1)
        try:
            element = page.locator(selector).first
            if element.is_visible():
                print("✅ 找到按钮！准备点击...")
                time.sleep(1)
                element.click()
                print(f"✅ 成功点击'换一换'按钮！(使用CSS: {selector})")
                time.sleep(2)  # 点击后等待
                return True
            else:
                print("⚠️  元素不可见，继续尝试...")
        except Exception as e:
            print(f"❌ CSS选择器 {selector} 失败: {e}")
        time.sleep(0.5)  # 每次尝试之间的间隔

    print("❌ 所有方法都失败了，未找到'换一换'按钮")
    time.sleep(2)
    return False

def wait_for_browser_close(page):
    """等待浏览器关闭"""
    print("浏览器已打开，直接关闭浏览器窗口即可终止脚本...")

    while True:
        try:
            page.title()  # 检查页面是否还存在
            time.sleep(0.5)
        except Exception:
            print("检测到浏览器已关闭，脚本即将退出...")
            break

def run(playwright):
    """主运行函数"""
    print("🚀 开始执行自动化脚本...")
    time.sleep(1)

    browser = playwright.chromium.launch(headless=False)
    page = browser.new_page()

    try:
        # 导航到B站
        print("📱 正在打开B站...")
        time.sleep(1)
        page.goto("https://www.bilibili.com")
        print(f"✅ 页面加载完成！页面标题: {page.title()}")
        time.sleep(2)

        # 等待页面加载完成
        print("⏳ 等待页面完全加载...")
        page.wait_for_load_state('networkidle')
        print("✅ 网络请求完成")
        time.sleep(3)  # 确保动态内容加载完成
        print("✅ 页面准备就绪")

        # 寻找并点击按钮
        print("\n🎯 开始执行核心任务...")
        success = find_and_click_refresh_button(page)

        if success:
            print("\n🎉 任务执行成功！")
        else:
            print("\n⚠️  任务执行失败，但浏览器将保持打开")

        print("\n💡 你可以继续在浏览器中操作")
        print("💡 直接关闭浏览器窗口即可终止脚本")

        # 等待用户关闭浏览器
        wait_for_browser_close(page)

    except KeyboardInterrupt:
        print("\n🛑 检测到Ctrl+C，正在关闭浏览器...")
        browser.close()
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        print("🔧 浏览器将保持打开以便调试")
        # 不自动关闭浏览器，让用户可以查看错误状态
        wait_for_browser_close(page)

    print("✨ 脚本执行完毕")

with sync_playwright() as playwright:
    run(playwright)