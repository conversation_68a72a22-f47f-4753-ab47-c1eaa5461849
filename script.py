import time
from playwright.sync_api import sync_playwright

def find_and_click_refresh_button(page):
    """寻找并点击'换一换'按钮"""
    print("正在寻找'换一换'按钮...")

    # 首选：使用官方推荐的 get_by_* 方法
    try:
        # 通过角色和文本查找按钮
        refresh_button = page.get_by_role("button", name="换一换")
        if refresh_button.is_visible():
            refresh_button.click()
            print("✅ 成功点击'换一换'按钮！(使用 get_by_role)")
            return True
    except Exception:
        pass

    # 次选：使用简单的CSS选择器
    css_selectors = [
        "button.roll-btn",           # 基于类名
        "button:has-text('换一换')",   # 基于文本内容
        ".roll-btn"                  # 简化的类选择器
    ]

    for selector in css_selectors:
        try:
            element = page.locator(selector).first
            if element.is_visible():
                element.click()
                print(f"✅ 成功点击'换一换'按钮！(使用CSS: {selector})")
                return True
        except Exception:
            continue

    print("❌ 未找到'换一换'按钮")
    return False

def wait_for_browser_close(page):
    """等待浏览器关闭"""
    print("浏览器已打开，直接关闭浏览器窗口即可终止脚本...")

    while True:
        try:
            page.title()  # 检查页面是否还存在
            time.sleep(0.5)
        except Exception:
            print("检测到浏览器已关闭，脚本即将退出...")
            break

def run(playwright):
    """主运行函数"""
    browser = playwright.chromium.launch(headless=False)
    page = browser.new_page()

    try:
        # 导航到B站
        print("正在打开B站...")
        page.goto("https://www.bilibili.com")
        print(f"页面标题: {page.title()}")

        # 等待页面加载完成
        page.wait_for_load_state('networkidle')
        time.sleep(2)  # 确保动态内容加载完成

        # 寻找并点击按钮
        find_and_click_refresh_button(page)

        # 等待用户关闭浏览器
        wait_for_browser_close(page)

    except KeyboardInterrupt:
        print("\n检测到Ctrl+C，正在关闭浏览器...")
        browser.close()
    except Exception as e:
        print(f"\n发生错误: {e}")
        browser.close()

    print("脚本已终止。")

with sync_playwright() as playwright:
    run(playwright)