import time
from playwright.sync_api import sync_playwright

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    page = browser.new_page()

    # 导航到你想要的网页
    page.goto("https://www.bilibili.com")

    # 打印页面的标题
    print(page.title())
    print("浏览器已打开，正在寻找'换一换'按钮...")

    try:
        # 等待页面加载完成
        page.wait_for_load_state('networkidle')
        time.sleep(2)  # 额外等待确保页面完全加载

        # 寻找"换一换"按钮的多种选择器
        selectors = [
            'button.roll-btn',  # 基于class
            'button[class*="roll-btn"]',  # 包含roll-btn的class
            'button:has-text("换一换")',  # 基于文本内容
            'button.primary-btn.roll-btn',  # 完整的class组合
            '[data-v-0935080e].roll-btn'  # 基于data属性
        ]

        button_found = False

        # 尝试不同的选择器
        for selector in selectors:
            try:
                # 等待按钮出现（最多等待5秒）
                button = page.wait_for_selector(selector, timeout=5000)
                if button:
                    print(f"找到'换一换'按钮，使用选择器: {selector}")

                    # 滚动到按钮位置确保可见
                    button.scroll_into_view_if_needed()
                    time.sleep(0.5)

                    # 点击按钮
                    button.click()
                    print("成功点击'换一换'按钮！")
                    button_found = True
                    break

            except Exception as e:
                print(f"选择器 {selector} 未找到按钮: {e}")
                continue

        if not button_found:
            print("未找到'换一换'按钮，可能页面结构已变化")
            print("正在尝试通过文本查找...")

            # 最后尝试：通过文本内容查找
            try:
                page.click('text=换一换')
                print("通过文本成功找到并点击'换一换'按钮！")
                button_found = True
            except Exception as e:
                print(f"通过文本查找也失败: {e}")

        # 持续检查浏览器是否还在运行
        while True:
            try:
                # 尝试获取页面标题，如果浏览器被关闭，这会抛出异常
                page.title()
                time.sleep(0.5)  # 每0.5秒检查一次
            except Exception:
                # 浏览器已被关闭
                print("检测到浏览器已关闭，脚本即将退出...")
                break

    except KeyboardInterrupt:
        print("\n检测到Ctrl+C，正在关闭浏览器...")
        browser.close()
    except Exception as e:
        print(f"\n发生错误: {e}")
        browser.close()

    print("脚本已终止。")

with sync_playwright() as playwright:
    run(playwright)