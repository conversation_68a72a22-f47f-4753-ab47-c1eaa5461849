import re
import time
from playwright.sync_api import Playwright, sync_playwright, expect

def click_with_delay(element, description, delay=2):
    """点击元素并添加延迟，同时显示执行信息"""
    print(f"🔍 正在寻找: {description}")
    try:
        if element.is_visible():
            element.click()
            print(f"✅ 成功点击: {description}")
        else:
            print(f"⚠️  元素不可见: {description}")
    except Exception as e:
        print(f"❌ 点击失败: {description} - {e}")

    print(f"⏳ 等待 {delay} 秒...")
    time.sleep(delay)

def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()

    print("🚀 开始执行自动化脚本...")

    # 导航到页面
    print("📱 正在打开页面...")
    page.goto("https://h5.chengzhuo.site/")
    print("✅ 页面加载完成")
    time.sleep(3)  # 等待页面完全加载

    # 等待页面加载完成
    page.wait_for_load_state('networkidle')

    # 步骤1：点击"全部同意"
    element1 = page.locator("uni-label").filter(has_text="全部同意").locator("div").nth(1)
    click_with_delay(element1, "全部同意按钮", 2)

    # 步骤2：点击"同意"
    element2 = page.get_by_text("同意", exact=True)
    click_with_delay(element2, "同意按钮", 2)

    # 步骤3：点击"进行中"
    element3 = page.get_by_text("进行中")
    click_with_delay(element3, "进行中按钮", 2)

    # 步骤4：点击"立即参与"
    element4 = page.get_by_text("立即参与")
    click_with_delay(element4, "立即参与按钮", 2)

    # 步骤5：点击"一键购买"
    element5 = page.get_by_text("一键购买")
    click_with_delay(element5, "一键购买按钮", 3)

    print("🎉 所有步骤执行完成！")
    print("🔍 浏览器将保持打开状态，你可以查看结果")
    print("💡 直接关闭浏览器窗口即可终止脚本")

    # 等待用户手动关闭浏览器
    try:
        while True:
            page.title()  # 检查页面是否还存在
            time.sleep(0.5)
    except Exception:
        print("👋 检测到浏览器已关闭，脚本即将退出...")

    print("✨ 脚本执行完毕")

with sync_playwright() as playwright:
    run(playwright)
