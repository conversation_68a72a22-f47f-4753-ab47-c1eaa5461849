import time
from playwright.sync_api import Playwright, sync_playwright

def run_with_wait(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()

    steps = [
        ("点击'全部同意'", lambda: page.get_by_text("全部同意").click()),
        ("点击'同意'", lambda: page.get_by_text("同意", exact=True).click()),
        ("点击'进行中'", lambda: page.get_by_text("进行中").click()),
        ("点击'立即参与'", lambda: page.get_by_text("立即参与").click()),
        ("点击'一键购买'", lambda: page.get_by_text("一键购买").click()),
    ]

    try:
        print("开始执行脚本...")
        page.goto("https://h5.chengzhuo.site/")
        print("成功打开页面...")
        page.wait_for_load_state('networkidle')

        for idx, (desc, action) in enumerate(steps, start=1):
            try:
                action()
            except Exception as e:
                print(f"❌ 卡在第{idx}步：{desc}，错误: {e}")
                break
        else:
            print("✅ 所有步骤执行完成")

        # 如果脚本成功执行，则调用等待函数
        wait_for_browser_close(page)

    except Exception as e:
        print(f"脚本执行失败: {e}")
    finally:
        browser.close()
        print("脚本已终止。")

def wait_for_browser_close(page):
    print("浏览器保持打开，关闭浏览器窗口即可终止脚本")
    try:
        while True:
            page.title()
            time.sleep(0.5)
    except Exception:
        print("浏览器已关闭，脚本退出")

with sync_playwright() as playwright:
    run_with_wait(playwright)