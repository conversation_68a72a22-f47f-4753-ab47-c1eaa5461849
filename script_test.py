import re
from playwright.sync_api import Playwright, sync_playwright, expect


def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()
    page.goto("https://h5.chengzhuo.site/")
    page.locator("uni-label").filter(has_text="全部同意").locator("div").nth(1).click()
    page.get_by_text("同意", exact=True).click()
    page.get_by_text("进行中").click()
    page.get_by_text("立即参与").click()
    page.get_by_text("一键购买").click()

    # ---------------------
    context.close()
    browser.close()


with sync_playwright() as playwright:
    run(playwright)
