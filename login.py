import time
from playwright.sync_api import Playwright, sync_playwright

def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()

    try:
        print("开始执行脚本...")

        # 导航到页面
        page.goto("https://h5.chengzhuo.site/")
        print("页面加载完成")

        # 依次执行自动化点击操作
        page.locator("uni-label").filter(has_text="全部同意").locator("div").nth(1).click()
        page.get_by_text("同意", exact=True).click()

        # 注意: 这里的定位器可能需要根据实际页面结构调整
        page.locator("div").filter(has_text="我的").nth(3).click()
        page.get_by_text("立即登录").click()

        # 提示用户输入账号密码
        username = input("请输入账号: ")
        password = input("请输入密码: ")

        # 填充账号和密码
        page.locator("input[type=\"text\"]").fill(username)
        page.locator("input[type=\"password\"]").fill(password)

        # 点击登录按钮
        page.get_by_text("登录", exact=True).click()
        print("\n所有自动化步骤已完成。")

        # 调用等待函数，直到用户手动关闭浏览器
        wait_for_browser_close(page)

    except Exception as e:
        print(f"\n❌ 脚本执行失败: {e}")
    finally:
        # 确保浏览器最终被关闭
        browser.close()
        print("脚本已终止。")

def wait_for_browser_close(page):
    """等待浏览器关闭"""
    print("浏览器保持打开，关闭浏览器窗口即可终止脚本")
    try:
        while True:
            page.title()
            time.sleep(0.5)
    except Exception:
        print("浏览器已关闭，脚本退出")

with sync_playwright() as playwright:
    run(playwright)